from typing import Dict, List, Optional
from datetime import datetime
import json
import requests
import os
from openai import OpenAI
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi, AMAP_TOOLS
from langflow_api_V4 import call_taxi_service
import time

class StateManager:
    """全局状态管理器"""
    def __init__(self):
        self.conversation_state = {}
        self.api_results = {}
        self.missing_params = {}
        self.execution_history = []
        self.current_status = {
            'last_action': None,
            'status': 'ready',  # ready, processing, success, failed
            'error': None
        }
        
    def log_execution(self, action: str, status: str, details: Dict):
        """记录执行历史"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.execution_history.append(entry)
        self.current_status = {
            'last_action': action,
            'status': status,
            'error': details.get('error')
        }
        
    def update_state(self, key: str, value: any):
        self.conversation_state[key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_api_call(self, api_name: str, result: Dict):
        self.api_results[api_name] = result
        
    def track_missing_param(self, param: str, question: str):
        self.missing_params[param] = question


class ToolAgent:
    """集成百炼function calling的工具调用Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        # self.client = BailianClient()
        
    async def call_api(self, endpoint: str, params: Dict) -> Dict:
        try:
            # 记录开始状态
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="processing",
                details={"params": params}
            )
            
            # 调用百炼function calling
            func_call = {
                "name": endpoint,
                "parameters": params
            }
            response = await self.client.function_calling(func_call)
            
            # 处理响应
            if response.status == "success":
                result = {
                    "status": True,
                    "data": response.data
                }
                self.state.log_api_call(endpoint, result)
                self.state.log_execution(
                    action=f"call_api:{endpoint}",
                    status="success",
                    details={"response": result}
                )
                return result
            else:
                raise Exception(response.error)
                
        except Exception as e:
            error_result = {
                "status": False,
                "error": str(e)
            }
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

class CompletionAgent:
    """条件补全Agent""" 
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        
    def generate_clarification(self, missing: Dict) -> str:
        questions = []
        for param, question in missing.items():
            questions.append(f"{question} (需要{param})")
        return "请提供以下信息:\n" + "\n".join(questions)

class DebugAgent:
    """Debug Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        self.error_log = []
        
    def log_error(self, error: str, context: Dict):
        entry = {
            "timestamp": datetime.now().isoformat(),
            "error": error,
            "context": context,
            "state": self.state.conversation_state
        }
        self.error_log.append(entry)
        
    def get_diagnosis(self) -> str:
        if not self.error_log:
            return "系统运行正常"
        return f"最近错误: {self.error_log[-1]['error']}"

class EnhancedTaxiAgent:
    """增强版打车Agent，集成高德地图和打车服务"""
    def __init__(self):
        self.state = StateManager()
        self.tool_agent = ToolAgent(self.state)
        self.completion_agent = CompletionAgent(self.state)
        self.debug_agent = DebugAgent(self.state)

        # 百炼模型配置
        api_key = os.getenv("BAILIAN_API_KEY")
        if api_key:
            self.bailian_client = OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            self.bailian_model_name = "qwen-max"
        else:
            print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能将不可用")
            self.bailian_client = None
            self.bailian_model_name = None

        # 初始化高德地图工具
        try:
            self.amap_tools = AmapMCPTools()
        except Exception as e:
            print(f"警告: 高德地图工具初始化失败: {e}")
            self.amap_tools = None

        # 系统提示词
        self.system_prompt = """你是一个智能打车助手，可以帮助用户：
1. 查询地点的经纬度坐标
2. 获取城市代码信息
3. 搜索POI（兴趣点）
4. 调用打车服务

当用户提到地点时，你可以使用高德地图工具获取准确的位置信息。
当用户要打车时，你可以调用打车服务为用户安排车辆。

请根据用户的需求选择合适的工具来帮助用户。"""

        self.context = {}  # 存储对话上下文

        # 定义可用的工具
        self.tools = [
            # 高德地图工具
            {
                "type": "function",
                "function": {
                    "name": "mcp_geocode_address",
                    "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "address": {
                                "type": "string",
                                "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                            }
                        },
                        "required": ["address"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_get_city_code",
                    "description": "获取城市的adcode（城市ID）和相关信息。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city_name": {
                                "type": "string",
                                "description": "城市名称，如'杭州'、'北京'、'上海'"
                            }
                        },
                        "required": ["city_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_search_poi",
                    "description": "搜索POI（兴趣点），如餐厅、酒店、景点等。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "keyword": {
                                "type": "string",
                                "description": "搜索关键词，如'星巴克'、'酒店'、'加油站'"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于限定搜索范围"
                            },
                            "types": {
                                "type": "string",
                                "description": "POI类型，可选，如'餐饮服务'、'住宿服务'"
                            }
                        },
                        "required": ["keyword"]
                    }
                }
            },
            # 打车服务工具
            {
                "type": "function",
                "function": {
                    "name": "call_taxi_service",
                    "description": "调用打车服务，为用户安排车辆从起点到终点。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "start_place": {
                                "type": "string",
                                "description": "出发地点名称，必须提供"
                            },
                            "end_place": {
                                "type": "string",
                                "description": "目的地名称，必须提供"
                            },
                            "car_prefer": {
                                "type": "string",
                                "description": "车辆偏好，可选，如'经济型'、'舒适型'、'豪华型'等"
                            }
                        },
                        "required": ["start_place", "end_place"]
                    }
                }
            }
        ]

    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息的主要方法"""
        try:
            # 记录开始状态
            self.state.log_execution(
                action="process_message",
                status="processing",
                details={"user_input": user_input, "session_id": session_id}
            )

            # 检查是否有可用的AI客户端
            if not self.bailian_client:
                return "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。"

            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]

            # 添加用户消息
            self.context[session_id].append({"role": "user", "content": user_input})

            # 调用百炼API获取响应
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                tool_choice="auto"
            )

            response_message = response.choices[0].message
            self.context[session_id].append(response_message)

            # 检查是否需要function calling
            if response_message.tool_calls:
                # 处理每个function call
                for tool_call in response_message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)

                    # 执行function
                    function_response = self._execute_function(
                        function_name,
                        function_args,
                        session_id
                    )

                    # 将结果添加到上下文
                    self.context[session_id].append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": function_name,
                        "content": json.dumps(function_response, ensure_ascii=False)
                    })

                # 获取最终回复
                final_response = self.bailian_client.chat.completions.create(
                    model=self.bailian_model_name,
                    messages=self.context[session_id]
                )

                final_message = final_response.choices[0].message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message
            else:
                # 直接返回响应
                final_message = response_message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            self.debug_agent.log_error(str(e), {"input": user_input, "session_id": session_id})
            self.state.log_execution(
                action="process_message",
                status="failed",
                details={"error": str(e)}
            )
            return error_msg

    def _execute_function(self, function_name: str, args: dict, session_id: str) -> dict:
        """执行function并处理结果"""
        try:
            # 记录function调用
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="processing",
                details={"args": args}
            )

            # 根据function name调用不同的工具
            if function_name == "mcp_geocode_address":
                result = mcp_geocode_address(
                    address=args["address"],
                    city=args.get("city")
                )
            elif function_name == "mcp_get_city_code":
                result = mcp_get_city_code(
                    city_name=args["city_name"]
                )
            elif function_name == "mcp_search_poi":
                result = mcp_search_poi(
                    keyword=args["keyword"],
                    city=args.get("city"),
                    types=args.get("types")
                )
            elif function_name == "call_taxi_service":
                # 调用打车服务并统一输出格式
                raw_result = call_taxi_service(
                    start_place=args["start_place"],
                    end_place=args["end_place"],
                    car_prefer=args.get("car_prefer", "")
                )
                # 统一格式化输出
                result = self._format_taxi_service_result(raw_result)
            else:
                raise ValueError(f"未知的工具: {function_name}")

            # 记录成功状态
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="success",
                details={"result": result}
            )

            return result

        except Exception as e:
            error_result = {"status": False, "error": str(e)}
            self.debug_agent.log_error(str(e), {
                "function": function_name,
                "args": args
            })
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

    def _format_taxi_service_result(self, raw_result: dict) -> dict:
        """统一格式化打车服务的返回结果"""
        try:
            # 检查原始结果格式
            if not isinstance(raw_result, dict):
                return {
                    "status": False,
                    "error": "打车服务返回格式错误",
                    "raw_result": raw_result
                }

            # 统一格式化
            formatted_result = {
                "status": raw_result.get("status", 0) == 1,  # 1表示成功
                "message": raw_result.get("action_ret_msg", ""),
                "error_code": raw_result.get("err_code", ""),
                "error_message": raw_result.get("err_msg", ""),
                "time_cost": raw_result.get("time_cost", 0),
                "details": {}
            }

            # 提取详细信息
            if "params" in raw_result:
                params = raw_result["params"]
                formatted_result["details"] = {
                    "pickup_name": params.get("pickup_name", ""),
                    "pickup_location": {
                        "longitude": params.get("pickup_l", ""),
                        "latitude": params.get("pickup_r", "")
                    },
                    "destination_name": params.get("dest_name", ""),
                    "destination_location": {
                        "longitude": params.get("dest_l", ""),
                        "latitude": params.get("dest_r", "")
                    },
                    "sub_id": params.get("sub_id", ""),
                    "output": params.get("output", "")
                }

            # 保留原始结果用于调试
            formatted_result["raw_data"] = raw_result

            return formatted_result

        except Exception as e:
            return {
                "status": False,
                "error": f"格式化打车服务结果时出错: {str(e)}",
                "raw_result": raw_result
            }

    def _handle_scene_function(self, function_name: str, args: dict) -> dict:
        """处理场景相关的function调用"""
        # 这里可以根据不同场景实现不同逻辑
        return {
            "status": True,
            "data": args,
            "message": f"成功处理场景: {function_name}"
        }

    # 保持其他原有方法不变...
    def process_user_input(self, user_input: str) -> str:
        # 原有实现...
        pass

    def _detect_intent(self, text: str) -> str:
        # 原有实现...
        pass

    def _extract_parameters(self, text: str, intent: str) -> Dict:
        # 原有实现...
        pass

    def _check_missing_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _prepare_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _generate_success_response(self, intent: str, result: Dict) -> str:
        # 原有实现...
        pass

    def _handle_error(self, intent: str, error_result: Dict) -> str:
        # 原有实现...
        pass

class MainAgent:
    """保持向后兼容的主控Agent"""
    def __init__(self):
        self.enhanced_agent = EnhancedTaxiAgent()

    def process_user_input(self, user_input: str) -> str:
        return self.enhanced_agent.process_message(user_input)


def test_enhanced_taxi_agent():
    """测试增强版打车Agent"""
    print("=== 测试增强版打车Agent ===")

    agent = EnhancedTaxiAgent()

    # 测试用例
    test_cases = [
        "西湖在哪里？",
        "帮我查一下杭州的城市代码",
        "北京有哪些星巴克？",
        "我要从康德大厦打车到太阳宫",
        "从北京站到首都机场，要舒适型车辆"
    ]

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        try:
            response = agent.process_message(test_input, f"test_session_{i}")
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 50)


def test_call_taxi_service_format():
    """测试 call_taxi_service 的格式化输出"""
    print("\n=== 测试 call_taxi_service 格式化输出 ===")

    agent = EnhancedTaxiAgent()

    # 直接测试打车服务
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "test_session"
        )

        print("格式化后的结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    # 测试增强版Agent
    test_enhanced_taxi_agent()

    # 测试打车服务格式化
    test_call_taxi_service_format()

    # 保持向后兼容
    print("\n=== 向后兼容测试 ===")
    agent = MainAgent()
    print(agent.process_user_input("我要从康德大厦打车到太阳宫"))
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_confirm_origin_and_destination",
#             "description": "用户确认了出发地和目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "start_place": {"type": "string"},
#                     "start_alias": {"type": "string"},
#                     "end_place": {"type": "string"},
#                     "end_alias": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_reject_recommendation",
#             "description": "用户拒绝了推荐的出发地或目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_confirm_ride",
#             "description": "用户确认可以打车，但未涉及确认出发地或目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_inquire_taxi_info",
#             "description": "用户询问与打车相关的信息，如车型、价格、距离等。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_other",
#             "description": "用户最后一轮不属于以上各种情况，则可能用户的请求你暂不支持。输出talk来告知并安抚用户暂不支持的请求",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     }
# ]
    

#     def main_process(self, user_input: str, session_id:  str) -> str:
#         if session_id in self.contenct:
#             self.contenct[session_id].append({"role": "user", "content": user_input})
#         else:
#             self.contenct[session_id] = [{"role": "system", "content": self.system_prompt},{"role": "user", "content": user_input}]


#         try:
#             return self.process_user_input(user_input)
#         except:
#             completion = self.bailian_client.chat.completions.create(
#                         model=self.bailian_model_name,
#                         messages=self.contenct[session_id],
#                         extra_body={"enable_thinking": False},
#                         tools=self.tools  # 'tools' is accessible as it's a global variable
#                     )
            
#             completion_json = json.loads(completion)
#             self.contenct[session_id].append({"role": "assistant", "content": user_input})
#             if completion_json["choices"][0]["finish_reason"]== "tool_calls":
#                 self.contenct[session_id].append({"role": "assistant", "content": user_input})
#                 completion = self.bailian_client.chat.completions.create(
#                         model=self.bailian_model_name,
#                         messages=self.contenct[session_id],
#                         extra_body={"enable_thinking": False}
#                     )
                
#                 message = completion_json["choices"][0]["message"]
#                 function_name = message["function_call"]["name"]
#                 arguments = json.loads(message["function_call"]["arguments"])

#                 # 调用本地function并获取结果

#                 function_response = get_ride_details(**arguments)

#                 # 将function的执行结果加入上下文
#                 messages.append(message)  # 加入模型提出的function call请求
#                 messages.append({
#                     "role": "function",
#                     "name": function_name,
#                     "content": json.dumps(function_response)
#                 })

#                 # # 再次调用OpenAI API继续对话
#                 # second_response = self.bailian_client.chat.completions.create(
#                 #         model=self.bailian_model_name,
#                 #         messages=self.contenct[session_id],
#                 #         extra_body={"enable_thinking": False}
#                 #     )
#                 # 显示最终回复
#                 final_message = second_response["choices"][0]["message"]["content"]
#                 print(final_message)
#                 # 增加function  调用结果重新执行
#             else:
#                 self.contenct[session_id].append({"role": "assistant", "content": user_input})


#                 return completion.model_dump_json()


#     def process_user_input(self, user_input: str) -> str:

#         # 1. 意图识别
#         intent = self._detect_intent(user_input)
#         self.state.update_state("current_intent", intent)
        
#         # 2. 参数提取
#         extracted = self._extract_parameters(user_input, intent)
        
#         # 3. 检查缺失参数
#         missing = self._check_missing_params(intent, extracted)
#         if missing:
#             self.state.track_missing_param(intent, missing)
#             return self.completion_agent.generate_clarification(missing)
            
#         # 4. 调用工具
#         tool_config = self.available_tools[intent]
#         result = self.tool_agent.call_api(
#             tool_config["endpoint"],
#             self._prepare_params(intent, extracted)
#         )
        
#         # 5. 处理结果
#         if result.get("status", False):
#             return self._generate_success_response(intent, result)
#         else:
#             self.debug_agent.log_error(result.get("error", "Unknown error"), 
#                                      {"intent": intent})
#             return self._handle_error(intent, result)

#     def _detect_intent(self, text: str) -> str:
#         """简单意图识别"""
#         text = text.lower()
#         if "上车点" in text or "哪里上车" in text:
#             return "search_spots"
#         elif "多少钱" in text or "价格" in text:
#             return "estimate_price"
#         elif "叫车" in text or "打车" in text:
#             return "order_car"
#         return "unknown"

#     def _extract_parameters(self, text: str, intent: str) -> Dict:
#         """简单参数提取"""
#         # 实际实现应使用更复杂的NLP技术
#         params = {}
#         if "西湖" in text:
#             params.update({
#                 "longitude": "120.12",
#                 "latitude": "30.28"
#             })
#         if "杭州东站" in text:
#             params.update({
#                 "to_longitude": "120.21", 
#                 "to_latitude": "30.29"
#             })
#         return params

#     def _check_missing_params(self, intent: str, params: Dict) -> Dict:
#         """检查缺失参数"""
#         required = self.available_tools[intent]["required_params"]
#         missing = {}
#         for param in required:
#             if param not in params:
#                 missing[param] = f"请提供{param.replace('_', ' ')}"
#         return missing

#     def _prepare_params(self, intent: str, params: Dict) -> Dict:
#         """准备API请求参数"""
#         base_params = {
#             "context": json.dumps({
#                 "req_id": str(datetime.now().timestamp()),
#                 "uid": "user_" + str(hash(datetime.now().isoformat())),
#                 "coords": f"{params.get('longitude', '')},{params.get('latitude', '')}"
#             }),
#             "scriptId": "1000009",
#             "subInteractiveName": self.available_tools[intent]["endpoint"],
#             "scriptParams": json.dumps(params),
#             "debug": "false"
#         }
#         return base_params

#     def _generate_success_response(self, intent: str, result: Dict) -> str:
#         """生成成功响应"""
#         data = json.loads(result["data"])
#         if intent == "search_spots":
#             spots = data["list"][:3]
#             return "附近上车点:\n" + "\n".join(
#                 f"{i+1}. {s['name']} (距离{s['distance']}米)" 
#                 for i, s in enumerate(spots)
#             )
#         elif intent == "estimate_price":
#             prices = data["list"]
#             return "预估价格:\n" + "\n".join(
#                 f"- {p['name']}: ¥{p['price']/100}"
#                 for p in prices
#             )
#         elif intent == "order_car":
#             order = data
#             return (
#                 f"叫车成功!\n"
#                 f"订单号: {order['order_id']}\n"
#                 f"司机: {order['driver_info_vo']['name']}\n"
#                 f"车型: {order['driver_info_vo']['car_type']}\n"
#                 f"预计到达时间: 5分钟内"
#             )
#         return "操作成功完成"

#     def _handle_error(self, intent: str, error_result: Dict) -> str:
#         """错误处理"""
#         error = error_result.get("error", "未知错误")
#         diagnosis = self.debug_agent.get_diagnosis()
        
#         return (
#             f"抱歉，处理您的请求时出错\n"
#             f"错误: {error}\n"
#             f"诊断: {diagnosis}\n"
#             f"请稍后再试或提供更多信息"
#         )

# # 使用示例
# if __name__ == "__main__":
#     agent = MainAgent()
    
#     # 模拟对话
#     print(agent.process_user_input("西湖附近哪里有上车点？"))
#     print(agent.process_user_input("从西湖到杭州东站打车多少钱？"))
#     print(agent.process_user_input("我要从西湖叫车去杭州东站"))
