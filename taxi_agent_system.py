from typing import Dict, List, Optional
from datetime import datetime
import json
import requests
import os
from openai import OpenAI

class StateManager:
    """全局状态管理器"""
    def __init__(self):
        self.conversation_state = {}
        self.api_results = {}
        self.missing_params = {}
        self.execution_history = []
        self.current_status = {
            'last_action': None,
            'status': 'ready',  # ready, processing, success, failed
            'error': None
        }
        
    def log_execution(self, action: str, status: str, details: Dict):
        """记录执行历史"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.execution_history.append(entry)
        self.current_status = {
            'last_action': action,
            'status': status,
            'error': details.get('error')
        }
        
    def update_state(self, key: str, value: any):
        self.conversation_state[key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_api_call(self, api_name: str, result: Dict):
        self.api_results[api_name] = result
        
    def track_missing_param(self, param: str, question: str):
        self.missing_params[param] = question


class ToolAgent:
    """集成百炼function calling的工具调用Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        # self.client = BailianClient()
        
    async def call_api(self, endpoint: str, params: Dict) -> Dict:
        try:
            # 记录开始状态
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="processing",
                details={"params": params}
            )
            
            # 调用百炼function calling
            func_call = {
                "name": endpoint,
                "parameters": params
            }
            response = await self.client.function_calling(func_call)
            
            # 处理响应
            if response.status == "success":
                result = {
                    "status": True,
                    "data": response.data
                }
                self.state.log_api_call(endpoint, result)
                self.state.log_execution(
                    action=f"call_api:{endpoint}",
                    status="success",
                    details={"response": result}
                )
                return result
            else:
                raise Exception(response.error)
                
        except Exception as e:
            error_result = {
                "status": False,
                "error": str(e)
            }
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

class CompletionAgent:
    """条件补全Agent""" 
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        
    def generate_clarification(self, missing: Dict) -> str:
        questions = []
        for param, question in missing.items():
            questions.append(f"{question} (需要{param})")
        return "请提供以下信息:\n" + "\n".join(questions)

class DebugAgent:
    """Debug Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        self.error_log = []
        
    def log_error(self, error: str, context: Dict):
        entry = {
            "timestamp": datetime.now().isoformat(),
            "error": error,
            "context": context,
            "state": self.state.conversation_state
        }
        self.error_log.append(entry)
        
    def get_diagnosis(self) -> str:
        if not self.error_log:
            return "系统运行正常"
        return f"最近错误: {self.error_log[-1]['error']}"

class MainAgent:
    """主控Agent"""
    def __init__(self):
        self.state = StateManager()
        self.tool_agent = ToolAgent(self.state)
        self.completion_agent = CompletionAgent(self.state)
        self.debug_agent = DebugAgent(self.state)

        # 百炼模型配置
        self.bailian_client = OpenAI(
            api_key=os.getenv("BAILIAN_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        self.bailian_model_name = "qwen-max"
        
        # 注册可用工具
        self.available_tools = {
            "search_spots": {
                "endpoint": "search_spot_by_coordinate",
                "required_params": ["longitude", "latitude"]
            },
            "estimate_price": {
                "endpoint": "get_estimate_price",
                "required_params": ["from_longitude", "from_latitude", 
                                  "to_longitude", "to_latitude"]
            },
            "order_car": {
                "endpoint": "order_car",
                "required_params": ["from_longitude", "from_latitude",
                                   "to_longitude", "to_latitude",
                                   "caller_phone"]
            }
        }
        
        self.system_prompt = """你是一个智能多智能体打车系统的主控Agent...""" # 保持原有系统提示
        
        self.context = {}  # 存储对话上下文
        self.tools = [...]  # 保持原有工具定义

    def main_process(self, user_input: str, session_id: str) -> str:
        """处理用户输入，支持function calling和二次询问"""
        # 初始化或更新对话上下文
        if session_id not in self.context:
            self.context[session_id] = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]
        else:
            self.context[session_id].append({"role": "user", "content": user_input})

        try:
            # 调用百炼API获取响应
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                tool_choice="auto"
            )
            
            response_message = response.choices[0].message
            self.context[session_id].append(response_message)

            # 检查是否需要function calling
            if response_message.tool_calls:
                # 处理每个function call
                for tool_call in response_message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    # 执行function
                    function_response = self._execute_function(
                        function_name, 
                        function_args,
                        session_id
                    )
                    
                    # 将结果添加到上下文
                    self.context[session_id].append({
                        "role": "tool",
                        "name": function_name,
                        "content": json.dumps(function_response)
                    })
                
                # 获取最终回复
                final_response = self.bailian_client.chat.completions.create(
                    model=self.bailian_model_name,
                    messages=self.context[session_id]
                )
                
                final_message = final_response.choices[0].message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })
                
                return final_message
            else:
                # 直接返回响应
                return response_message.content

        except Exception as e:
            self.debug_agent.log_error(str(e), {"input": user_input})
            return f"处理请求时出错: {str(e)}"

    def _execute_function(self, function_name: str, args: dict, session_id: str) -> dict:
        """执行本地function并处理结果"""
        try:
            # 根据function name调用不同逻辑
            if function_name.startswith("scene_"):
                return self._handle_scene_function(function_name, args)
            else:
                # 调用工具API
                tool_config = self.available_tools.get(function_name)
                if tool_config:
                    result = self.tool_agent.call_api(
                        tool_config["endpoint"],
                        args
                    )
                    return result
                else:
                    raise ValueError(f"未知的工具: {function_name}")
        except Exception as e:
            self.debug_agent.log_error(str(e), {
                "function": function_name,
                "args": args
            })
            return {"status": False, "error": str(e)}

    def _handle_scene_function(self, function_name: str, args: dict) -> dict:
        """处理场景相关的function调用"""
        # 这里可以根据不同场景实现不同逻辑
        return {
            "status": True,
            "data": args,
            "message": f"成功处理场景: {function_name}"
        }

    # 保持其他原有方法不变...
    def process_user_input(self, user_input: str) -> str:
        # 原有实现...
        pass

    def _detect_intent(self, text: str) -> str:
        # 原有实现...
        pass

    def _extract_parameters(self, text: str, intent: str) -> Dict:
        # 原有实现...
        pass

    def _check_missing_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _prepare_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _generate_success_response(self, intent: str, result: Dict) -> str:
        # 原有实现...
        pass

    def _handle_error(self, intent: str, error_result: Dict) -> str:
        # 原有实现...
        pass

if __name__ == "__main__":
    agent = MainAgent()
    print(agent.process_user_input("西湖附近哪里有上车点？"))
    print(agent.process_user_input("从西湖到杭州东站打车多少钱？"))
    print(agent.process_user_input("我要从西湖叫车去杭州东站"))
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_confirm_origin_and_destination",
#             "description": "用户确认了出发地和目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "start_place": {"type": "string"},
#                     "start_alias": {"type": "string"},
#                     "end_place": {"type": "string"},
#                     "end_alias": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_reject_recommendation",
#             "description": "用户拒绝了推荐的出发地或目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_confirm_ride",
#             "description": "用户确认可以打车，但未涉及确认出发地或目的地。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_inquire_taxi_info",
#             "description": "用户询问与打车相关的信息，如车型、价格、距离等。",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     },
#     {
#         "type": "function",
#         "function": {
#             "name": "scene_other",
#             "description": "用户最后一轮不属于以上各种情况，则可能用户的请求你暂不支持。输出talk来告知并安抚用户暂不支持的请求",
#             "parameters": {
#                 "type": "object",
#                 "properties": {
#                     "talk": {"type": "string"}
#                 },
#                 "required": []
#             }
#         }
#     }
# ]
    

#     def main_process(self, user_input: str, session_id:  str) -> str:
#         if session_id in self.contenct:
#             self.contenct[session_id].append({"role": "user", "content": user_input})
#         else:
#             self.contenct[session_id] = [{"role": "system", "content": self.system_prompt},{"role": "user", "content": user_input}]


#         try:
#             return self.process_user_input(user_input)
#         except:
#             completion = self.bailian_client.chat.completions.create(
#                         model=self.bailian_model_name,
#                         messages=self.contenct[session_id],
#                         extra_body={"enable_thinking": False},
#                         tools=self.tools  # 'tools' is accessible as it's a global variable
#                     )
            
#             completion_json = json.loads(completion)
#             self.contenct[session_id].append({"role": "assistant", "content": user_input})
#             if completion_json["choices"][0]["finish_reason"]== "tool_calls":
#                 self.contenct[session_id].append({"role": "assistant", "content": user_input})
#                 completion = self.bailian_client.chat.completions.create(
#                         model=self.bailian_model_name,
#                         messages=self.contenct[session_id],
#                         extra_body={"enable_thinking": False}
#                     )
                
#                 message = completion_json["choices"][0]["message"]
#                 function_name = message["function_call"]["name"]
#                 arguments = json.loads(message["function_call"]["arguments"])

#                 # 调用本地function并获取结果

#                 function_response = get_ride_details(**arguments)

#                 # 将function的执行结果加入上下文
#                 messages.append(message)  # 加入模型提出的function call请求
#                 messages.append({
#                     "role": "function",
#                     "name": function_name,
#                     "content": json.dumps(function_response)
#                 })

#                 # # 再次调用OpenAI API继续对话
#                 # second_response = self.bailian_client.chat.completions.create(
#                 #         model=self.bailian_model_name,
#                 #         messages=self.contenct[session_id],
#                 #         extra_body={"enable_thinking": False}
#                 #     )
#                 # 显示最终回复
#                 final_message = second_response["choices"][0]["message"]["content"]
#                 print(final_message)
#                 # 增加function  调用结果重新执行
#             else:
#                 self.contenct[session_id].append({"role": "assistant", "content": user_input})


#                 return completion.model_dump_json()


#     def process_user_input(self, user_input: str) -> str:

#         # 1. 意图识别
#         intent = self._detect_intent(user_input)
#         self.state.update_state("current_intent", intent)
        
#         # 2. 参数提取
#         extracted = self._extract_parameters(user_input, intent)
        
#         # 3. 检查缺失参数
#         missing = self._check_missing_params(intent, extracted)
#         if missing:
#             self.state.track_missing_param(intent, missing)
#             return self.completion_agent.generate_clarification(missing)
            
#         # 4. 调用工具
#         tool_config = self.available_tools[intent]
#         result = self.tool_agent.call_api(
#             tool_config["endpoint"],
#             self._prepare_params(intent, extracted)
#         )
        
#         # 5. 处理结果
#         if result.get("status", False):
#             return self._generate_success_response(intent, result)
#         else:
#             self.debug_agent.log_error(result.get("error", "Unknown error"), 
#                                      {"intent": intent})
#             return self._handle_error(intent, result)

#     def _detect_intent(self, text: str) -> str:
#         """简单意图识别"""
#         text = text.lower()
#         if "上车点" in text or "哪里上车" in text:
#             return "search_spots"
#         elif "多少钱" in text or "价格" in text:
#             return "estimate_price"
#         elif "叫车" in text or "打车" in text:
#             return "order_car"
#         return "unknown"

#     def _extract_parameters(self, text: str, intent: str) -> Dict:
#         """简单参数提取"""
#         # 实际实现应使用更复杂的NLP技术
#         params = {}
#         if "西湖" in text:
#             params.update({
#                 "longitude": "120.12",
#                 "latitude": "30.28"
#             })
#         if "杭州东站" in text:
#             params.update({
#                 "to_longitude": "120.21", 
#                 "to_latitude": "30.29"
#             })
#         return params

#     def _check_missing_params(self, intent: str, params: Dict) -> Dict:
#         """检查缺失参数"""
#         required = self.available_tools[intent]["required_params"]
#         missing = {}
#         for param in required:
#             if param not in params:
#                 missing[param] = f"请提供{param.replace('_', ' ')}"
#         return missing

#     def _prepare_params(self, intent: str, params: Dict) -> Dict:
#         """准备API请求参数"""
#         base_params = {
#             "context": json.dumps({
#                 "req_id": str(datetime.now().timestamp()),
#                 "uid": "user_" + str(hash(datetime.now().isoformat())),
#                 "coords": f"{params.get('longitude', '')},{params.get('latitude', '')}"
#             }),
#             "scriptId": "1000009",
#             "subInteractiveName": self.available_tools[intent]["endpoint"],
#             "scriptParams": json.dumps(params),
#             "debug": "false"
#         }
#         return base_params

#     def _generate_success_response(self, intent: str, result: Dict) -> str:
#         """生成成功响应"""
#         data = json.loads(result["data"])
#         if intent == "search_spots":
#             spots = data["list"][:3]
#             return "附近上车点:\n" + "\n".join(
#                 f"{i+1}. {s['name']} (距离{s['distance']}米)" 
#                 for i, s in enumerate(spots)
#             )
#         elif intent == "estimate_price":
#             prices = data["list"]
#             return "预估价格:\n" + "\n".join(
#                 f"- {p['name']}: ¥{p['price']/100}"
#                 for p in prices
#             )
#         elif intent == "order_car":
#             order = data
#             return (
#                 f"叫车成功!\n"
#                 f"订单号: {order['order_id']}\n"
#                 f"司机: {order['driver_info_vo']['name']}\n"
#                 f"车型: {order['driver_info_vo']['car_type']}\n"
#                 f"预计到达时间: 5分钟内"
#             )
#         return "操作成功完成"

#     def _handle_error(self, intent: str, error_result: Dict) -> str:
#         """错误处理"""
#         error = error_result.get("error", "未知错误")
#         diagnosis = self.debug_agent.get_diagnosis()
        
#         return (
#             f"抱歉，处理您的请求时出错\n"
#             f"错误: {error}\n"
#             f"诊断: {diagnosis}\n"
#             f"请稍后再试或提供更多信息"
#         )

# # 使用示例
# if __name__ == "__main__":
#     agent = MainAgent()
    
#     # 模拟对话
#     print(agent.process_user_input("西湖附近哪里有上车点？"))
#     print(agent.process_user_input("从西湖到杭州东站打车多少钱？"))
#     print(agent.process_user_input("我要从西湖叫车去杭州东站"))
